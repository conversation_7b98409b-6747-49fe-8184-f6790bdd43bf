import 'package:equatable/equatable.dart';

/// Domain entity representing the subscription status of a user
class SubscriptionStatus extends Equatable {
  const SubscriptionStatus({
    required this.isActive,
    required this.entitlements,
    required this.activeSubscriptions,
    required this.allPurchasedProductIdentifiers,
    required this.nonSubscriptionTransactions,
    this.originalAppUserId,
    this.firstSeen,
    this.originalPurchaseDate,
    this.requestDate,
    this.managementURL,
  });

  /// Whether the user has any active subscription
  final bool isActive;

  /// Map of entitlement identifiers to entitlement info
  final Map<String, EntitlementInfo> entitlements;

  /// Map of active subscription product identifiers to subscription info
  final Map<String, SubscriptionInfo> activeSubscriptions;

  /// Set of all purchased product identifiers (including non-subscriptions)
  final Set<String> allPurchasedProductIdentifiers;

  /// List of non-subscription transactions
  final List<Transaction> nonSubscriptionTransactions;

  /// Original app user ID
  final String? originalAppUserId;

  /// Date when the user was first seen
  final DateTime? firstSeen;

  /// Date of the original purchase
  final DateTime? originalPurchaseDate;

  /// Date when this info was requested
  final DateTime? requestDate;

  /// URL for managing subscriptions
  final String? managementURL;

  /// Check if user has access to a specific entitlement
  bool hasEntitlement(String entitlementIdentifier) {
    final entitlement = entitlements[entitlementIdentifier];
    return entitlement?.isActive ?? false;
  }

  /// Get expiration date for a specific entitlement
  DateTime? getExpirationDate(String entitlementIdentifier) {
    final entitlement = entitlements[entitlementIdentifier];
    return entitlement?.expirationDate;
  }

  @override
  List<Object?> get props => [
        isActive,
        entitlements,
        activeSubscriptions,
        allPurchasedProductIdentifiers,
        nonSubscriptionTransactions,
        originalAppUserId,
        firstSeen,
        originalPurchaseDate,
        requestDate,
        managementURL,
      ];
}

/// Represents entitlement information
class EntitlementInfo extends Equatable {
  const EntitlementInfo({
    required this.identifier,
    required this.isActive,
    required this.willRenew,
    required this.productIdentifier,
    required this.isSandbox,
    this.latestPurchaseDate,
    this.originalPurchaseDate,
    this.expirationDate,
    this.unsubscribeDetectedAt,
    this.billingIssueDetectedAt,
  });

  /// Entitlement identifier
  final String identifier;

  /// Whether this entitlement is currently active
  final bool isActive;

  /// Whether this entitlement will renew
  final bool willRenew;

  /// Product identifier that unlocked this entitlement
  final String productIdentifier;

  /// Whether this is a sandbox purchase
  final bool isSandbox;

  /// Date of the latest purchase for this entitlement
  final DateTime? latestPurchaseDate;

  /// Date of the original purchase for this entitlement
  final DateTime? originalPurchaseDate;

  /// Expiration date for this entitlement
  final DateTime? expirationDate;

  /// Date when unsubscribe was detected
  final DateTime? unsubscribeDetectedAt;

  /// Date when billing issue was detected
  final DateTime? billingIssueDetectedAt;

  @override
  List<Object?> get props => [
        identifier,
        isActive,
        willRenew,
        productIdentifier,
        isSandbox,
        latestPurchaseDate,
        originalPurchaseDate,
        expirationDate,
        unsubscribeDetectedAt,
        billingIssueDetectedAt,
      ];
}

/// Represents subscription information
class SubscriptionInfo extends Equatable {
  const SubscriptionInfo({
    required this.productIdentifier,
    required this.isActive,
    required this.willRenew,
    required this.isSandbox,
    this.latestPurchaseDate,
    this.originalPurchaseDate,
    this.expirationDate,
    this.unsubscribeDetectedAt,
    this.billingIssueDetectedAt,
    this.periodType,
  });

  /// Product identifier for this subscription
  final String productIdentifier;

  /// Whether this subscription is currently active
  final bool isActive;

  /// Whether this subscription will renew
  final bool willRenew;

  /// Whether this is a sandbox purchase
  final bool isSandbox;

  /// Date of the latest purchase
  final DateTime? latestPurchaseDate;

  /// Date of the original purchase
  final DateTime? originalPurchaseDate;

  /// Expiration date
  final DateTime? expirationDate;

  /// Date when unsubscribe was detected
  final DateTime? unsubscribeDetectedAt;

  /// Date when billing issue was detected
  final DateTime? billingIssueDetectedAt;

  /// Period type (trial, intro, normal)
  final PeriodType? periodType;

  @override
  List<Object?> get props => [
        productIdentifier,
        isActive,
        willRenew,
        isSandbox,
        latestPurchaseDate,
        originalPurchaseDate,
        expirationDate,
        unsubscribeDetectedAt,
        billingIssueDetectedAt,
        periodType,
      ];
}

/// Represents a transaction
class Transaction extends Equatable {
  const Transaction({
    required this.productIdentifier,
    required this.purchaseDate,
    required this.transactionIdentifier,
  });

  /// Product identifier for this transaction
  final String productIdentifier;

  /// Date when the purchase was made
  final DateTime purchaseDate;

  /// Unique transaction identifier
  final String transactionIdentifier;

  @override
  List<Object> get props => [
        productIdentifier,
        purchaseDate,
        transactionIdentifier,
      ];
}

/// Enum representing different period types
enum PeriodType {
  trial,
  intro,
  normal,
}
