import 'package:equatable/equatable.dart';

/// Domain entity representing a subscription product
class SubscriptionProduct extends Equatable {
  const SubscriptionProduct({
    required this.identifier,
    required this.title,
    required this.description,
    required this.price,
    required this.priceString,
    required this.currencyCode,
    required this.introPrice,
    required this.subscriptionPeriod,
    this.isPopular = false,
  });

  /// Unique identifier for the product
  final String identifier;

  /// Display title of the product
  final String title;

  /// Description of the product
  final String description;

  /// Price in the smallest currency unit (e.g., cents)
  final double price;

  /// Formatted price string (e.g., "$9.99")
  final String priceString;

  /// Currency code (e.g., "USD")
  final String currencyCode;

  /// Introductory price information
  final IntroPrice? introPrice;

  /// Subscription period information
  final SubscriptionPeriod subscriptionPeriod;

  /// Whether this product should be highlighted as popular
  final bool isPopular;

  @override
  List<Object?> get props => [
        identifier,
        title,
        description,
        price,
        priceString,
        currencyCode,
        introPrice,
        subscriptionPeriod,
        isPopular,
      ];
}

/// Represents introductory pricing information
class IntroPrice extends Equatable {
  const IntroPrice({
    required this.price,
    required this.priceString,
    required this.period,
    required this.cycles,
  });

  /// Introductory price in the smallest currency unit
  final double price;

  /// Formatted introductory price string
  final String priceString;

  /// Period for the introductory price
  final SubscriptionPeriod period;

  /// Number of cycles for the introductory price
  final int cycles;

  @override
  List<Object> get props => [price, priceString, period, cycles];
}

/// Represents subscription period information
class SubscriptionPeriod extends Equatable {
  const SubscriptionPeriod({
    required this.unit,
    required this.value,
  });

  /// The unit of the subscription period
  final PeriodUnit unit;

  /// The value of the subscription period
  final int value;

  /// Get a human-readable description of the period
  String get description {
    switch (unit) {
      case PeriodUnit.day:
        return value == 1 ? 'Daily' : '$value days';
      case PeriodUnit.week:
        return value == 1 ? 'Weekly' : '$value weeks';
      case PeriodUnit.month:
        return value == 1 ? 'Monthly' : '$value months';
      case PeriodUnit.year:
        return value == 1 ? 'Yearly' : '$value years';
    }
  }

  @override
  List<Object> get props => [unit, value];
}

/// Enum representing subscription period units
enum PeriodUnit {
  day,
  week,
  month,
  year,
}
