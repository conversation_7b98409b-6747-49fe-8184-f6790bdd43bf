import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../entities/subscription_status.dart';
import '../repositories/subscription_repository.dart';

/// Use case for restoring previous purchases
@injectable
class RestorePurchasesUseCase {
  final SubscriptionRepository _repository;

  RestorePurchasesUseCase(this._repository);

  /// Execute the use case to restore previous purchases
  /// 
  /// Returns the updated subscription status or a failure
  Future<Either<SubscriptionFailure, SubscriptionStatus>> call() async {
    return await _repository.restorePurchases();
  }
}
