import 'package:get_it/get_it.dart';

import '../data/repositories/subscription_repository_impl.dart';
import '../domain/repositories/subscription_repository.dart';
import '../domain/usecases/check_entitlement_usecase.dart';
import '../domain/usecases/get_subscription_products_usecase.dart';
import '../domain/usecases/get_subscription_status_usecase.dart';
import '../domain/usecases/purchase_subscription_usecase.dart';
import '../domain/usecases/restore_purchases_usecase.dart';
import '../presentation/bloc/subscription_bloc.dart';
import '../services/revenue_cat_service.dart';

/// Dependency injection setup for subscription_temp feature
class SubscriptionTempInjection {
  static void init() {
    final sl = GetIt.instance;

    // Services
    sl.registerLazySingleton<RevenueCatService>(() => RevenueCatService());

    // Repository
    sl.registerLazySingleton<SubscriptionRepository>(
      () => SubscriptionRepositoryImpl(sl()),
    );

    // Use cases
    sl.registerLazySingleton(() => GetSubscriptionProductsUseCase(sl()));
    sl.registerLazySingleton(() => PurchaseSubscriptionUseCase(sl()));
    sl.registerLazySingleton(() => RestorePurchasesUseCase(sl()));
    sl.registerLazySingleton(() => GetSubscriptionStatusUseCase(sl()));
    sl.registerLazySingleton(() => CheckEntitlementUseCase(sl()));

    // BLoC
    sl.registerFactory(
      () => SubscriptionBloc(
        sl(),
        sl(),
        sl(),
        sl(),
        sl(),
        sl(),
      ),
    );
  }
}
