import 'package:flutter/material.dart';

import '../../../../common/extentions/colors_extension.dart';
import '../../../../common/widgets/app_gesture_detector.dart';
import '../../domain/entities/subscription_product.dart';

/// Widget for displaying a subscription product card
class SubscriptionProductCard extends StatelessWidget {
  const SubscriptionProductCard({
    super.key,
    required this.product,
    required this.onTap,
    this.isLoading = false,
  });

  final SubscriptionProduct product;
  final VoidCallback onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    final isMonthly = product.subscriptionPeriod.unit == PeriodUnit.month &&
        product.subscriptionPeriod.value == 1;
    final isYearly = product.subscriptionPeriod.unit == PeriodUnit.year &&
        product.subscriptionPeriod.value == 1;

    return Stack(
      children: [
        AppGestureDetector(
          onTap: isLoading ? null : onTap,
          child: Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.surface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: product.isPopular 
                    ? context.primaryColor 
                    : context.onSurface.withOpacity(0.2),
                width: product.isPopular ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header row with title and price
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getProductTitle(isMonthly, isYearly),
                              style: context.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: context.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getProductSubtitle(isMonthly, isYearly),
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            product.priceString,
                            style: context.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: context.onSurface,
                            ),
                          ),
                          if (isYearly) ...[
                            const SizedBox(height: 2),
                            Text(
                              'Save 20%',
                              style: context.textTheme.bodySmall?.copyWith(
                                color: context.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),

                  // Intro price if available
                  if (product.introPrice != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: context.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Start with ${product.introPrice!.priceString} for ${product.introPrice!.period.description}',
                        style: context.textTheme.bodySmall?.copyWith(
                          color: context.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Purchase button
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : onTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Text(
                              'Subscribe Now',
                              style: context.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Popular badge
        if (product.isPopular)
          Positioned(
            top: -1,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: context.primaryColor,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Text(
                'POPULAR',
                style: context.textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Get product title based on subscription period
  String _getProductTitle(bool isMonthly, bool isYearly) {
    if (isMonthly) return 'Monthly Plan';
    if (isYearly) return 'Yearly Plan';
    return product.title;
  }

  /// Get product subtitle based on subscription period
  String _getProductSubtitle(bool isMonthly, bool isYearly) {
    if (isMonthly) return 'Billed monthly';
    if (isYearly) return 'Billed annually';
    return product.subscriptionPeriod.description;
  }
}
