import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../domain/entities/subscription_product.dart';
import '../../domain/entities/subscription_status.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../../domain/usecases/check_entitlement_usecase.dart';
import '../../domain/usecases/get_subscription_products_usecase.dart';
import '../../domain/usecases/get_subscription_status_usecase.dart';
import '../../domain/usecases/purchase_subscription_usecase.dart';
import '../../domain/usecases/restore_purchases_usecase.dart';

part 'subscription_event.dart';
part 'subscription_state.dart';

/// BLoC for managing subscription operations using RevenueCat
/// Follows the status-based state management pattern for better consistency
@injectable
class SubscriptionBloc extends Bloc<SubscriptionEvent, SubscriptionState> {
  final SubscriptionRepository _repository;
  final GetSubscriptionProductsUseCase _getProductsUseCase;
  final PurchaseSubscriptionUseCase _purchaseUseCase;
  final RestorePurchasesUseCase _restoreUseCase;
  final GetSubscriptionStatusUseCase _getStatusUseCase;
  final CheckEntitlementUseCase _checkEntitlementUseCase;

  SubscriptionBloc(
    this._repository,
    this._getProductsUseCase,
    this._purchaseUseCase,
    this._restoreUseCase,
    this._getStatusUseCase,
    this._checkEntitlementUseCase,
  ) : super(const SubscriptionState()) {
    on<InitializeSubscription>(_onInitializeSubscription);
    on<LoadSubscriptionProducts>(_onLoadSubscriptionProducts);
    on<PurchaseSubscription>(_onPurchaseSubscription);
    on<RestoreSubscriptions>(_onRestoreSubscriptions);
    on<GetSubscriptionStatus>(_onGetSubscriptionStatus);
    on<CheckEntitlement>(_onCheckEntitlement);
    on<ResetSubscriptionState>(_onResetSubscriptionState);
    on<_SubscriptionStatusUpdated>(_onSubscriptionStatusUpdated);

    _setupSubscriptionStatusListener();
  }

  /// Initialize the subscription service
  Future<void> _onInitializeSubscription(
    InitializeSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    final result = await _repository.initialize(appUserId: event.appUserId);

    result.fold(
      (failure) => emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: failure.message,
      )),
      (_) => emit(state.copyWith(
        status: SubscriptionBlocStatus.initial,
        isInitialized: true,
        clearErrorMessage: true,
      )),
    );
  }

  /// Load available subscription products
  Future<void> _onLoadSubscriptionProducts(
    LoadSubscriptionProducts event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    final result = await _getProductsUseCase(offeringIdentifier: event.offeringIdentifier);

    result.fold(
      (failure) => emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: failure.message,
      )),
      (products) => emit(state.copyWith(
        status: SubscriptionBlocStatus.productsLoaded,
        products: products,
        clearErrorMessage: true,
      )),
    );
  }

  /// Purchase a subscription product
  Future<void> _onPurchaseSubscription(
    PurchaseSubscription event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.purchasing, clearErrorMessage: true));

    final result = await _purchaseUseCase(event.productIdentifier);

    result.fold(
      (failure) {
        // Handle purchase cancellation differently from other errors
        if (failure is PurchaseCancelledFailure) {
          // Return to previous state without showing error
          emit(state.copyWith(
            status: state.products.isNotEmpty 
                ? SubscriptionBlocStatus.productsLoaded 
                : SubscriptionBlocStatus.initial,
            clearErrorMessage: true,
          ));
        } else {
          emit(state.copyWith(
            status: SubscriptionBlocStatus.error,
            errorMessage: failure.message,
          ));
        }
      },
      (subscriptionStatus) => emit(state.copyWith(
        status: SubscriptionBlocStatus.purchaseSuccess,
        subscriptionStatus: subscriptionStatus,
        clearErrorMessage: true,
      )),
    );
  }

  /// Restore previous purchases
  Future<void> _onRestoreSubscriptions(
    RestoreSubscriptions event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    final result = await _restoreUseCase();

    result.fold(
      (failure) => emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: failure.message,
      )),
      (subscriptionStatus) => emit(state.copyWith(
        status: SubscriptionBlocStatus.restoreSuccess,
        subscriptionStatus: subscriptionStatus,
        clearErrorMessage: true,
      )),
    );
  }

  /// Get current subscription status
  Future<void> _onGetSubscriptionStatus(
    GetSubscriptionStatus event,
    Emitter<SubscriptionState> emit,
  ) async {
    emit(state.copyWith(status: SubscriptionBlocStatus.loading, clearErrorMessage: true));

    final result = await _getStatusUseCase();

    result.fold(
      (failure) => emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: failure.message,
      )),
      (subscriptionStatus) => emit(state.copyWith(
        status: SubscriptionBlocStatus.statusLoaded,
        subscriptionStatus: subscriptionStatus,
        clearErrorMessage: true,
      )),
    );
  }

  /// Check if user has access to a specific entitlement
  Future<void> _onCheckEntitlement(
    CheckEntitlement event,
    Emitter<SubscriptionState> emit,
  ) async {
    final result = await _checkEntitlementUseCase(event.entitlementIdentifier);

    result.fold(
      (failure) => emit(state.copyWith(
        status: SubscriptionBlocStatus.error,
        errorMessage: failure.message,
      )),
      (hasEntitlement) => emit(state.copyWith(
        status: SubscriptionBlocStatus.entitlementChecked,
        hasEntitlement: hasEntitlement,
        clearErrorMessage: true,
      )),
    );
  }

  /// Reset the subscription state
  void _onResetSubscriptionState(
    ResetSubscriptionState event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(const SubscriptionState());
  }

  /// Handle subscription status updates from the service
  void _onSubscriptionStatusUpdated(
    _SubscriptionStatusUpdated event,
    Emitter<SubscriptionState> emit,
  ) {
    emit(state.copyWith(
      subscriptionStatus: event.subscriptionStatus,
    ));
  }

  /// Set up listener for subscription status updates
  void _setupSubscriptionStatusListener() {
    _repository.setSubscriptionStatusUpdateListener((subscriptionStatus) {
      add(_SubscriptionStatusUpdated(subscriptionStatus));
    });
  }

  @override
  Future<void> close() {
    // Clean up any listeners if needed
    return super.close();
  }
}
