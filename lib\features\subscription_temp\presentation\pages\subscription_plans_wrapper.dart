import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/di/injection.dart';
import '../bloc/subscription_bloc.dart';
import 'subscription_plans_screen.dart';

/// Wrapper widget that provides SubscriptionBloc to SubscriptionPlansScreen
class SubscriptionPlansWrapper extends StatelessWidget {
  const SubscriptionPlansWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<SubscriptionBloc>()
        ..add(const InitializeSubscription()),
      child: const SubscriptionPlansScreen(),
    );
  }
}
