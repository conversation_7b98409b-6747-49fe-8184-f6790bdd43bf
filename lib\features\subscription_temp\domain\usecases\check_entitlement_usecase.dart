import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../repositories/subscription_repository.dart';

/// Use case for checking if user has access to a specific entitlement
@injectable
class CheckEntitlementUseCase {
  final SubscriptionRepository _repository;

  CheckEntitlementUseCase(this._repository);

  /// Execute the use case to check entitlement access
  /// 
  /// [entitlementIdentifier] - The identifier of the entitlement to check
  /// Returns true if user has access, false otherwise, or a failure
  Future<Either<SubscriptionFailure, bool>> call(String entitlementIdentifier) async {
    return await _repository.hasEntitlement(entitlementIdentifier);
  }
}
