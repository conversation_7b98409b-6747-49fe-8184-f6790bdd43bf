import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../entities/subscription_product.dart';
import '../repositories/subscription_repository.dart';

/// Use case for getting available subscription products
@injectable
class GetSubscriptionProductsUseCase {
  final SubscriptionRepository _repository;

  GetSubscriptionProductsUseCase(this._repository);

  /// Execute the use case to get subscription products
  /// 
  /// [offeringIdentifier] - Optional identifier for a specific offering
  /// Returns a list of available subscription products or a failure
  Future<Either<SubscriptionFailure, List<SubscriptionProduct>>> call({
    String? offeringIdentifier,
  }) async {
    return await _repository.getProducts(offeringIdentifier: offeringIdentifier);
  }
}
