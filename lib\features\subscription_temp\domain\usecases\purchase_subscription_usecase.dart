import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../entities/subscription_status.dart';
import '../repositories/subscription_repository.dart';

/// Use case for purchasing a subscription
@injectable
class PurchaseSubscriptionUseCase {
  final SubscriptionRepository _repository;

  PurchaseSubscriptionUseCase(this._repository);

  /// Execute the use case to purchase a subscription
  /// 
  /// [productIdentifier] - The identifier of the product to purchase
  /// Returns the updated subscription status or a failure
  Future<Either<SubscriptionFailure, SubscriptionStatus>> call(
    String productIdentifier,
  ) async {
    return await _repository.purchaseProduct(productIdentifier);
  }
}
