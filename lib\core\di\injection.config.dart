// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i1;
import 'package:in_app_purchase/in_app_purchase.dart' as _i7;
import 'package:injectable/injectable.dart' as _i2;
import 'package:isar/isar.dart' as _i8;

import '../../features/home/<USER>/datasources/home_remote_datasource.dart'
    as _i35;
import '../../features/home/<USER>/datasources/local_food_data_source.dart'
    as _i9;
import '../../features/home/<USER>/repositories/exercise_repository_impl.dart'
    as _i34;
import '../../features/home/<USER>/repositories/food_repository_impl.dart'
    as _i30;
import '../../features/home/<USER>/repositories/home_repository_impl.dart'
    as _i37;
import '../../features/home/<USER>/repositories/exercise_repository.dart'
    as _i33;
import '../../features/home/<USER>/repositories/food_repository.dart' as _i29;
import '../../features/home/<USER>/repositories/home_repository.dart' as _i36;
import '../../features/home/<USER>/usecases/delete_exercise_usecase.dart'
    as _i50;
import '../../features/home/<USER>/usecases/get_daily_exercises_usecase.dart'
    as _i53;
import '../../features/home/<USER>/usecases/get_daily_user_data_usecase.dart'
    as _i54;
import '../../features/home/<USER>/usecases/update_daily_user_data_usecase.dart'
    as _i49;
import '../../features/home/<USER>/bloc/nutrition_bloc/bloc/nutrition_bloc.dart'
    as _i62;
import '../../features/home/<USER>/bloc/recent_activity_bloc.dart'
    as _i65;
import '../../features/main/data/repo/main_repo_impl.dart' as _i39;
import '../../features/main/domain/repo/main_repo.dart' as _i38;
import '../../features/main/presentation/bloc/main_bloc.dart' as _i61;
import '../../features/onboarding/data/datasource/onboarding_remote_datasource.dart'
    as _i12;
import '../../features/onboarding/data/repositories/onboarding_repository_imp.dart'
    as _i41;
import '../../features/onboarding/domain/repositories/onboarding_repository.dart'
    as _i40;
import '../../features/onboarding/domain/usecases/get_user_local_user_case.dart'
    as _i58;
import '../../features/onboarding/domain/usecases/local_delete_clear_user_use_case.dart'
    as _i59;
import '../../features/onboarding/domain/usecases/local_save_update_user_use_case.dart'
    as _i60;
import '../../features/onboarding/domain/usecases/submit_onboarding_usecase.dart'
    as _i47;
import '../../features/onboarding/presentation/bloc/onboarding_bloc.dart'
    as _i63;
import '../../features/quick_actions/exercise/data/datasources/exercise_local_datasource.dart'
    as _i24;
import '../../features/quick_actions/exercise/data/datasources/exercise_remote_datasource.dart'
    as _i25;
import '../../features/quick_actions/exercise/data/repositories/exercise_repository_impl.dart'
    as _i27;
import '../../features/quick_actions/exercise/domain/repositories/exercise_repository.dart'
    as _i26;
import '../../features/quick_actions/exercise/domain/services/calorie_calculation_service.dart'
    as _i4;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_ai_usecase.dart'
    as _i44;
import '../../features/quick_actions/exercise/domain/usecases/save_exercise_usecase.dart'
    as _i45;
import '../../features/quick_actions/exercise/presentation/bloc/exercise_bloc.dart'
    as _i70;
import '../../features/quick_actions/food_database/data/datasources/food_remote_data_source.dart'
    as _i28;
import '../../features/quick_actions/food_database/data/datasources/local_food_database_data_source.dart'
    as _i10;
import '../../features/quick_actions/food_database/data/repositories/food_database_repository_impl.dart'
    as _i52;
import '../../features/quick_actions/food_database/domain/repositories/food_database_repository.dart'
    as _i51;
import '../../features/quick_actions/food_database/domain/usecases/add_selected_food_usecase.dart'
    as _i3;
import '../../features/quick_actions/food_database/domain/usecases/create_meal_use_case.dart'
    as _i68;
import '../../features/quick_actions/food_database/domain/usecases/delete_meal_usecase.dart'
    as _i69;
import '../../features/quick_actions/food_database/domain/usecases/get_database_food_usecase.dart'
    as _i55;
import '../../features/quick_actions/food_database/domain/usecases/get_my_meals_usecase.dart'
    as _i56;
import '../../features/quick_actions/food_database/domain/usecases/get_recent_food_usecase.dart'
    as _i57;
import '../../features/quick_actions/food_database/domain/usecases/post_meal_to_log.dart'
    as _i64;
import '../../features/quick_actions/food_database/domain/usecases/remove_selected_food_usecase.dart'
    as _i13;
import '../../features/quick_actions/food_database/domain/usecases/save_and_create_meal_usecase.dart'
    as _i71;
import '../../features/quick_actions/food_database/domain/usecases/search_meals_use_case.dart'
    as _i67;
import '../../features/quick_actions/food_database/presentation/bloc/food_database_bloc.dart'
    as _i72;
import '../../features/quick_actions/scan_food/data/datasources/scan_food_remote_datasource.dart'
    as _i15;
import '../../features/quick_actions/scan_food/data/repositories/scan_food_repository_impl.dart'
    as _i17;
import '../../features/quick_actions/scan_food/domain/repositories/scan_food_repository.dart'
    as _i16;
import '../../features/quick_actions/scan_food/domain/usecases/recognize_food_usecase.dart'
    as _i22;
import '../../features/quick_actions/scan_food/domain/usecases/scan_barcode_use_case.dart'
    as _i46;
import '../../features/quick_actions/scan_food/presentation/bloc/scan_food_bloc.dart'
    as _i66;
import '../../features/subscription_temp/data/repositories/subscription_repository_impl.dart'
    as _i20;
import '../../features/subscription_temp/domain/repositories/subscription_repository.dart'
    as _i19;
import '../../features/subscription_temp/domain/usecases/check_entitlement_usecase.dart'
    as _i23;
import '../../features/subscription_temp/domain/usecases/get_subscription_products_usecase.dart'
    as _i31;
import '../../features/subscription_temp/domain/usecases/get_subscription_status_usecase.dart'
    as _i32;
import '../../features/subscription_temp/domain/usecases/purchase_subscription_usecase.dart'
    as _i42;
import '../../features/subscription_temp/domain/usecases/restore_purchases_usecase.dart'
    as _i43;
import '../../features/subscription_temp/presentation/bloc/subscription_bloc.dart'
    as _i48;
import '../../features/subscription_temp/services/revenue_cat_service.dart'
    as _i14;
import '../datasources/streak_local_data_source.dart' as _i18;
import '../datasources/user_local_data_source.dart' as _i21;
import '../isar_initialization.dart' as _i73;
import '../local_models/daily_data_model/daily_user_info_service.dart' as _i5;
import '../network/http_client.dart' as _i6;
import '../network/interceptor.dart' as _i11;

extension GetItInjectableX on _i1.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i1.GetIt> init({
    String? environment,
    _i2.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i2.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final registerModule = _$RegisterModule();
    gh.factory<_i3.AddSelectedFoodUseCase>(() => _i3.AddSelectedFoodUseCase());
    gh.factory<_i4.CalorieCalculationService>(
        () => _i4.CalorieCalculationService());
    gh.lazySingleton<_i5.DailyUserInfoService>(
        () => _i5.DailyUserInfoService());
    gh.lazySingleton<_i6.HTTPClient>(() => _i6.DioClient());
    gh.lazySingleton<_i7.InAppPurchase>(() => registerModule.inAppPurchase);
    await gh.factoryAsync<_i8.Isar>(
      () => registerModule.isar,
      preResolve: true,
    );
    gh.factory<_i9.LocalFoodDataSource>(
        () => _i9.LocalFoodDataSource(gh<_i8.Isar>()));
    gh.factory<_i10.LocalFoodDatabaseDataSource>(
        () => _i10.LocalFoodDatabaseDataSource(gh<_i8.Isar>()));
    gh.lazySingleton<_i11.LoggerInterceptor>(() => _i11.LoggerInterceptor());
    gh.lazySingleton<_i12.OnboardingRemoteDatasource>(() =>
        _i12.OnboardingRemoteDatasource(httpClient: gh<_i6.HTTPClient>()));
    gh.factory<_i13.RemoveSelectedFoodUseCase>(
        () => _i13.RemoveSelectedFoodUseCase());
    gh.factory<_i14.RevenueCatService>(() => _i14.RevenueCatService());
    gh.lazySingleton<_i15.ScanFoodRemoteDataSource>(
        () => _i15.ScanFoodRemoteDataSource(httpClient: gh<_i6.HTTPClient>()));
    gh.lazySingleton<_i16.ScanFoodRepository>(
        () => _i17.ScanFoodRepositoryImpl(gh<_i15.ScanFoodRemoteDataSource>()));
    gh.factory<_i18.StreakLocalDataSource>(
        () => _i18.StreakLocalDataSource(gh<_i8.Isar>()));
    gh.factory<_i19.SubscriptionRepository>(
        () => _i20.SubscriptionRepositoryImpl(gh<_i14.RevenueCatService>()));
    gh.factory<_i21.UserLocalDataSource>(
        () => _i21.UserLocalDataSource(gh<_i8.Isar>()));
    gh.lazySingleton<_i22.AnalyzeFoodUseCase>(
        () => _i22.AnalyzeFoodUseCase(gh<_i16.ScanFoodRepository>()));
    gh.factory<_i23.CheckEntitlementUseCase>(
        () => _i23.CheckEntitlementUseCase(gh<_i19.SubscriptionRepository>()));
    gh.factory<_i24.ExerciseLocalDataSource>(
        () => _i24.ExerciseLocalDataSourceImpl(gh<_i8.Isar>()));
    gh.factory<_i25.ExerciseRemoteDataSource>(() =>
        _i25.ExerciseRemoteDataSourceImpl(httpClient: gh<_i6.HTTPClient>()));
    gh.factory<_i26.ExerciseRepository>(() => _i27.ExerciseRepositoryImpl(
          remoteDataSource: gh<_i25.ExerciseRemoteDataSource>(),
          localDataSource: gh<_i24.ExerciseLocalDataSource>(),
        ));
    gh.lazySingleton<_i28.FoodRemoteDataSource>(
        () => _i28.FoodRemoteDataSource(dioClient: gh<_i6.HTTPClient>()));
    gh.factory<_i29.FoodRepository>(() => _i30.FoodRepositoryImpl(
        localDataSource: gh<_i9.LocalFoodDataSource>()));
    gh.factory<_i31.GetSubscriptionProductsUseCase>(() =>
        _i31.GetSubscriptionProductsUseCase(gh<_i19.SubscriptionRepository>()));
    gh.factory<_i32.GetSubscriptionStatusUseCase>(() =>
        _i32.GetSubscriptionStatusUseCase(gh<_i19.SubscriptionRepository>()));
    gh.factory<_i33.HomeExerciseRepository>(() =>
        _i34.HomeExerciseRepositoryImpl(
            exerciseLocalDataSource: gh<_i24.ExerciseLocalDataSource>()));
    gh.lazySingleton<_i35.HomeLocalDataSource>(
        () => _i35.HomeLocalDataSourceImpl(isar: gh<_i8.Isar>()));
    gh.lazySingleton<_i36.HomeRepository>(() => _i37.HomeRepositoryImpl(
        localDataSource: gh<_i35.HomeLocalDataSource>()));
    gh.factory<_i38.MainRepo>(() =>
        _i39.MainRepoImpl(localDataSource: gh<_i18.StreakLocalDataSource>()));
    gh.lazySingleton<_i40.OnboardingRepository>(
        () => _i41.OnboardingRepositoryImp(
              onboardingRemoteDatasource: gh<_i12.OnboardingRemoteDatasource>(),
              userLocalDataSource: gh<_i21.UserLocalDataSource>(),
            ));
    gh.factory<_i42.PurchaseSubscriptionUseCase>(() =>
        _i42.PurchaseSubscriptionUseCase(gh<_i19.SubscriptionRepository>()));
    gh.factory<_i43.RestorePurchasesUseCase>(
        () => _i43.RestorePurchasesUseCase(gh<_i19.SubscriptionRepository>()));
    gh.factory<_i44.SaveExerciseAiUseCase>(
        () => _i44.SaveExerciseAiUseCase(gh<_i26.ExerciseRepository>()));
    gh.factory<_i45.SaveExerciseUseCase>(
        () => _i45.SaveExerciseUseCase(gh<_i26.ExerciseRepository>()));
    gh.lazySingleton<_i46.ScanBarcodeUseCase>(() => _i46.ScanBarcodeUseCase(
        scanFoodRepository: gh<_i16.ScanFoodRepository>()));
    gh.lazySingleton<_i47.SubmitOnboardingUsecase>(() =>
        _i47.SubmitOnboardingUsecase(
            onboardingRepository: gh<_i40.OnboardingRepository>()));
    gh.factory<_i48.SubscriptionBloc>(() => _i48.SubscriptionBloc(
          gh<_i19.SubscriptionRepository>(),
          gh<_i31.GetSubscriptionProductsUseCase>(),
          gh<_i42.PurchaseSubscriptionUseCase>(),
          gh<_i43.RestorePurchasesUseCase>(),
          gh<_i32.GetSubscriptionStatusUseCase>(),
          gh<_i23.CheckEntitlementUseCase>(),
        ));
    gh.factory<_i49.UpdateDailyUserDataUseCase>(() =>
        _i49.UpdateDailyUserDataUseCase(repository: gh<_i36.HomeRepository>()));
    gh.factory<_i50.DeleteExerciseUseCase>(
        () => _i50.DeleteExerciseUseCase(gh<_i33.HomeExerciseRepository>()));
    gh.factory<_i51.FoodDatabaseRepository>(
        () => _i52.FoodDatabaseRepositoryImpl(
              gh<_i28.FoodRemoteDataSource>(),
              localDataSource: gh<_i10.LocalFoodDatabaseDataSource>(),
            ));
    gh.factory<_i53.GetDailyExercisesUseCase>(
        () => _i53.GetDailyExercisesUseCase(gh<_i33.HomeExerciseRepository>()));
    gh.factory<_i54.GetDailyUserDataUseCase>(() =>
        _i54.GetDailyUserDataUseCase(repository: gh<_i36.HomeRepository>()));
    gh.factory<_i55.GetDatabaseFoodUsecase>(() => _i55.GetDatabaseFoodUsecase(
        repository: gh<_i51.FoodDatabaseRepository>()));
    gh.factory<_i56.GetMyMealsUsecase>(() =>
        _i56.GetMyMealsUsecase(repository: gh<_i51.FoodDatabaseRepository>()));
    gh.factory<_i57.GetRecentFoodUsecase>(() => _i57.GetRecentFoodUsecase(
        repository: gh<_i51.FoodDatabaseRepository>()));
    gh.lazySingleton<_i58.GetUserLocalUserCase>(() => _i58.GetUserLocalUserCase(
        onboardingRepository: gh<_i40.OnboardingRepository>()));
    gh.lazySingleton<_i59.LocalDeleteClearUserUseCase>(() =>
        _i59.LocalDeleteClearUserUseCase(
            onboardingRepository: gh<_i40.OnboardingRepository>()));
    gh.lazySingleton<_i60.LocalSaveUpdateUserUseCase>(() =>
        _i60.LocalSaveUpdateUserUseCase(
            onboardingRepository: gh<_i40.OnboardingRepository>()));
    gh.factory<_i61.MainBloc>(() => _i61.MainBloc(gh<_i38.MainRepo>()));
    gh.factory<_i62.NutritionBloc>(() => _i62.NutritionBloc(
        getDailyUserDataUseCase: gh<_i54.GetDailyUserDataUseCase>()));
    gh.factory<_i63.OnboardingBloc>(() => _i63.OnboardingBloc(
          submitOnboardingUsecase: gh<_i47.SubmitOnboardingUsecase>(),
          localDeleteClearUserUseCase: gh<_i59.LocalDeleteClearUserUseCase>(),
          localSaveUpdateUserUseCase: gh<_i60.LocalSaveUpdateUserUseCase>(),
          getUserLocalUserCase: gh<_i58.GetUserLocalUserCase>(),
        ));
    gh.lazySingleton<_i64.PostMealToLog>(() => _i64.PostMealToLog(
        foodDatabaseRepository: gh<_i51.FoodDatabaseRepository>()));
    gh.singleton<_i65.RecentActivityBloc>(() => _i65.RecentActivityBloc(
          foodRepository: gh<_i29.FoodRepository>(),
          exerciseRepository: gh<_i33.HomeExerciseRepository>(),
          getDailyUserDataUseCase: gh<_i54.GetDailyUserDataUseCase>(),
          updateDailyUserDataUseCase: gh<_i49.UpdateDailyUserDataUseCase>(),
          getDailyExercisesUseCase: gh<_i53.GetDailyExercisesUseCase>(),
          deleteExerciseUseCase: gh<_i50.DeleteExerciseUseCase>(),
        ));
    gh.factory<_i66.ScanFoodBloc>(() => _i66.ScanFoodBloc(
          analyzeFoodUseCase: gh<_i22.AnalyzeFoodUseCase>(),
          scanBarcodeUseCase: gh<_i46.ScanBarcodeUseCase>(),
          recentActivityBloc: gh<_i65.RecentActivityBloc>(),
        ));
    gh.lazySingleton<_i67.SearchMealsUseCase>(() => _i67.SearchMealsUseCase(
        foodDatabaseRepository: gh<_i51.FoodDatabaseRepository>()));
    gh.lazySingleton<_i68.CreateMealUseCase>(() => _i68.CreateMealUseCase(
        foodDatabaseRepository: gh<_i51.FoodDatabaseRepository>()));
    gh.factory<_i69.DeleteMealUseCase>(() =>
        _i69.DeleteMealUseCase(repository: gh<_i51.FoodDatabaseRepository>()));
    gh.factory<_i70.ExerciseBloc>(() => _i70.ExerciseBloc(
          saveExerciseUseCase: gh<_i45.SaveExerciseUseCase>(),
          saveExerciseAiUseCase: gh<_i44.SaveExerciseAiUseCase>(),
          recentActivityBloc: gh<_i65.RecentActivityBloc>(),
        ));
    gh.factory<_i71.SaveAndCreateMealUseCase>(
        () => _i71.SaveAndCreateMealUseCase(
              repository: gh<_i51.FoodDatabaseRepository>(),
              createMealUseCase: gh<_i68.CreateMealUseCase>(),
            ));
    gh.factory<_i72.FoodDatabaseBloc>(() => _i72.FoodDatabaseBloc(
          foodDatabaseRepository: gh<_i51.FoodDatabaseRepository>(),
          searchMealsUseCase: gh<_i67.SearchMealsUseCase>(),
          postMealToLog: gh<_i64.PostMealToLog>(),
          createMealUseCase: gh<_i68.CreateMealUseCase>(),
          addSelectedFoodUseCase: gh<_i3.AddSelectedFoodUseCase>(),
          removeSelectedFoodUseCase: gh<_i13.RemoveSelectedFoodUseCase>(),
          saveAndCreateMealUseCase: gh<_i71.SaveAndCreateMealUseCase>(),
          deleteMealUseCase: gh<_i69.DeleteMealUseCase>(),
        ));
    return this;
  }
}

class _$RegisterModule extends _i73.RegisterModule {}
