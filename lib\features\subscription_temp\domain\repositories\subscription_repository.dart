import 'package:dartz/dartz.dart';
import '../entities/subscription_product.dart';
import '../entities/subscription_status.dart';

/// Repository interface for subscription operations
abstract class SubscriptionRepository {
  /// Initialize the subscription service
  Future<Either<SubscriptionFailure, void>> initialize({String? appUserId});

  /// Get available subscription products
  Future<Either<SubscriptionFailure, List<SubscriptionProduct>>> getProducts({
    String? offeringIdentifier,
  });

  /// Purchase a subscription product
  Future<Either<SubscriptionFailure, SubscriptionStatus>> purchaseProduct(
    String productIdentifier,
  );

  /// Restore previous purchases
  Future<Either<SubscriptionFailure, SubscriptionStatus>> restorePurchases();

  /// Get current subscription status
  Future<Either<SubscriptionFailure, SubscriptionStatus>> getSubscriptionStatus();

  /// Check if user has access to a specific entitlement
  Future<Either<SubscriptionFailure, bool>> hasEntitlement(String entitlementIdentifier);

  /// Login with app user ID
  Future<Either<SubscriptionFailure, SubscriptionStatus>> login(String appUserId);

  /// Logout current user
  Future<Either<SubscriptionFailure, SubscriptionStatus>> logout();

  /// Set up listener for subscription status updates
  void setSubscriptionStatusUpdateListener(Function(SubscriptionStatus) onUpdate);

  /// Remove subscription status update listener
  void removeSubscriptionStatusUpdateListener(Function(SubscriptionStatus) listener);
}

/// Represents failures that can occur during subscription operations
abstract class SubscriptionFailure {
  const SubscriptionFailure(this.message);
  final String message;
}

/// Failure when initialization fails
class InitializationFailure extends SubscriptionFailure {
  const InitializationFailure(super.message);
}

/// Failure when fetching products fails
class ProductFetchFailure extends SubscriptionFailure {
  const ProductFetchFailure(super.message);
}

/// Failure when purchase fails
class PurchaseFailure extends SubscriptionFailure {
  const PurchaseFailure(super.message);
}

/// Failure when purchase is cancelled by user
class PurchaseCancelledFailure extends SubscriptionFailure {
  const PurchaseCancelledFailure() : super('Purchase was cancelled by user');
}

/// Failure when restore purchases fails
class RestoreFailure extends SubscriptionFailure {
  const RestoreFailure(super.message);
}

/// Failure when getting subscription status fails
class StatusFetchFailure extends SubscriptionFailure {
  const StatusFetchFailure(super.message);
}

/// Failure when login fails
class LoginFailure extends SubscriptionFailure {
  const LoginFailure(super.message);
}

/// Failure when logout fails
class LogoutFailure extends SubscriptionFailure {
  const LogoutFailure(super.message);
}

/// Failure when network is unavailable
class NetworkFailure extends SubscriptionFailure {
  const NetworkFailure() : super('Network connection is unavailable');
}

/// Failure when service is unavailable
class ServiceUnavailableFailure extends SubscriptionFailure {
  const ServiceUnavailableFailure() : super('Subscription service is unavailable');
}

/// Generic failure for unknown errors
class UnknownFailure extends SubscriptionFailure {
  const UnknownFailure(super.message);
}
