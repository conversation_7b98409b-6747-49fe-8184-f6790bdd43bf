import 'package:dartz/dartz.dart';
import 'package:injectable/injectable.dart';

import '../entities/subscription_status.dart';
import '../repositories/subscription_repository.dart';

/// Use case for getting current subscription status
@injectable
class GetSubscriptionStatusUseCase {
  final SubscriptionRepository _repository;

  GetSubscriptionStatusUseCase(this._repository);

  /// Execute the use case to get current subscription status
  /// 
  /// Returns the current subscription status or a failure
  Future<Either<SubscriptionFailure, SubscriptionStatus>> call() async {
    return await _repository.getSubscriptionStatus();
  }
}
