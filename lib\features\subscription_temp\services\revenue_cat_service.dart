import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

/// Service class for handling RevenueCat operations
/// Provides a clean interface for subscription management using RevenueCat SDK
@injectable
class RevenueCatService {
  static const String _appleApiKey = 'YOUR_APPLE_API_KEY_HERE';
  static const String _googleApiKey = 'YOUR_GOOGLE_API_KEY_HERE';
  
  bool _isInitialized = false;
  CustomerInfo? _currentCustomerInfo;
  List<StoreProduct> _availableProducts = [];
  
  /// Initialize RevenueCat SDK
  /// Should be called early in the app lifecycle
  Future<void> initialize({String? appUserId}) async {
    if (_isInitialized) {
      debugPrint('RevenueCat already initialized');
      return;
    }

    try {
      // Enable debug logs in debug mode
      if (kDebugMode) {
        await Purchases.setLogLevel(LogLevel.debug);
      }

      // Configure RevenueCat with platform-specific API keys
      final apiKey = Platform.isIOS ? _appleApiKey : _googleApiKey;
      await Purchases.configure(PurchasesConfiguration(apiKey));

      // Set app user ID if provided
      if (appUserId != null) {
        await Purchases.logIn(appUserId);
      }


      
      // Get initial customer info
      _currentCustomerInfo = await Purchases.getCustomerInfo();
      
      _isInitialized = true;
      debugPrint('RevenueCat initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize RevenueCat: $e');
      rethrow;
    }
  }

  /// Get current offerings from RevenueCat
  /// Offerings contain the products configured in RevenueCat dashboard
  Future<Offerings?> getOfferings() async {
    _ensureInitialized();
    
    try {
      final offerings = await Purchases.getOfferings();
      return offerings;
    } catch (e) {
      debugPrint('Failed to get offerings: $e');
      return null;
    }
  }

  /// Get available products for a specific offering
  Future<List<StoreProduct>> getProducts({String? offeringIdentifier}) async {
    _ensureInitialized();
    
    try {
      final offerings = await getOfferings();
      if (offerings == null) return [];

      Offering? targetOffering;
      if (offeringIdentifier != null) {
        targetOffering = offerings.getOffering(offeringIdentifier);
      } else {
        targetOffering = offerings.current;
      }

      if (targetOffering == null) return [];

      _availableProducts = targetOffering.availablePackages
          .map((package) => package.storeProduct)
          .toList();
      
      return _availableProducts;
    } catch (e) {
      debugPrint('Failed to get products: $e');
      return [];
    }
  }

  /// Purchase a package
  Future<CustomerInfo?> purchasePackage(Package package) async {
    _ensureInitialized();
    
    try {
      final purchaserInfo = await Purchases.purchasePackage(package);
      _currentCustomerInfo = purchaserInfo.customerInfo;
      return _currentCustomerInfo;
    } catch (e) {
      debugPrint('Purchase failed: $e');
      rethrow;
    }
  }

  /// Purchase a product by identifier
  Future<CustomerInfo?> purchaseProduct(String productIdentifier) async {
    _ensureInitialized();
    
    try {
      final purchaserInfo = await Purchases.purchaseStoreProduct(
        _availableProducts.firstWhere(
          (product) => product.identifier == productIdentifier,
        ),
      );
      _currentCustomerInfo = purchaserInfo.customerInfo;
      return _currentCustomerInfo;
    } catch (e) {
      debugPrint('Purchase failed: $e');
      rethrow;
    }
  }

  /// Restore purchases
  Future<CustomerInfo?> restorePurchases() async {
    _ensureInitialized();
    
    try {
      _currentCustomerInfo = await Purchases.restorePurchases();
      return _currentCustomerInfo;
    } catch (e) {
      debugPrint('Restore purchases failed: $e');
      rethrow;
    }
  }

  /// Get current customer info
  Future<CustomerInfo> getCustomerInfo() async {
    _ensureInitialized();
    
    try {
      _currentCustomerInfo = await Purchases.getCustomerInfo();
      return _currentCustomerInfo!;
    } catch (e) {
      debugPrint('Failed to get customer info: $e');
      rethrow;
    }
  }

  /// Check if user has active subscription for a specific entitlement
  bool hasActiveSubscription(String entitlementIdentifier) {
    if (_currentCustomerInfo == null) return false;
    
    final entitlement = _currentCustomerInfo!.entitlements.all[entitlementIdentifier];
    return entitlement?.isActive ?? false;
  }

  /// Check if user has any active subscription
  bool hasAnyActiveSubscription() {
    if (_currentCustomerInfo == null) return false;
    
    return _currentCustomerInfo!.entitlements.active.isNotEmpty;
  }

  /// Get the expiration date of a specific entitlement
  DateTime? getExpirationDate(String entitlementIdentifier) {
    if (_currentCustomerInfo == null) return null;

    final entitlement = _currentCustomerInfo!.entitlements.all[entitlementIdentifier];
    return entitlement?.expirationDate != null
        ? DateTime.parse(entitlement!.expirationDate!)
        : null;
  }

  /// Login with app user ID
  Future<CustomerInfo> login(String appUserId) async {
    _ensureInitialized();
    
    try {
      final result = await Purchases.logIn(appUserId);
      _currentCustomerInfo = result.customerInfo;
      return _currentCustomerInfo!;
    } catch (e) {
      debugPrint('Login failed: $e');
      rethrow;
    }
  }

  /// Set up listener for customer info updates
  void setCustomerInfoUpdateListener(Function(CustomerInfo) onCustomerInfoUpdate) {
    Purchases.addCustomerInfoUpdateListener(onCustomerInfoUpdate);
  }

  /// Remove customer info update listener
  void removeCustomerInfoUpdateListener(Function(CustomerInfo) listener) {
    Purchases.removeCustomerInfoUpdateListener(listener);
  }

  /// Ensure RevenueCat is initialized before making calls
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('RevenueCat not initialized. Call initialize() first.');
    }
  }

  /// Getters
  bool get isInitialized => _isInitialized;
  CustomerInfo? get currentCustomerInfo => _currentCustomerInfo;
  List<StoreProduct> get availableProducts => _availableProducts;
}
