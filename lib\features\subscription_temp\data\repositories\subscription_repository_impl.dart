import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../domain/entities/subscription_product.dart';
import '../../domain/entities/subscription_status.dart';
import '../../domain/repositories/subscription_repository.dart';
import '../../services/revenue_cat_service.dart';
import '../models/subscription_product_model.dart';
import '../models/subscription_status_model.dart';

/// Implementation of SubscriptionRepository using RevenueCat
@Injectable(as: SubscriptionRepository)
class SubscriptionRepositoryImpl implements SubscriptionRepository {
  final RevenueCatService _revenueCatService;

  SubscriptionRepositoryImpl(this._revenueCatService);

  @override
  Future<Either<SubscriptionFailure, void>> initialize({String? appUserId}) async {
    try {
      await _revenueCatService.initialize(appUserId: appUserId);
      return const Right(null);
    } catch (e) {
      debugPrint('Subscription initialization failed: $e');
      return Left(InitializationFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, List<SubscriptionProduct>>> getProducts({
    String? offeringIdentifier,
  }) async {
    try {
      final products = await _revenueCatService.getProducts(
        offeringIdentifier: offeringIdentifier,
      );

      final subscriptionProducts = products
          .map((product) => SubscriptionProductModel.fromStoreProduct(
                product,
                isPopular: _isPopularProduct(product.identifier),
              ))
          .toList();

      return Right(subscriptionProducts);
    } catch (e) {
      debugPrint('Failed to get products: $e');
      return Left(ProductFetchFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, SubscriptionStatus>> purchaseProduct(
    String productIdentifier,
  ) async {
    try {
      final customerInfo = await _revenueCatService.purchaseProduct(productIdentifier);
      if (customerInfo == null) {
        return const Left(PurchaseFailure('Purchase failed - no customer info returned'));
      }

      final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
      return Right(subscriptionStatus);
    } on PurchasesErrorCode catch (e) {
      debugPrint('Purchase failed with error code: $e');
      
      if (e == PurchasesErrorCode.purchaseCancelledError) {
        return const Left(PurchaseCancelledFailure());
      } else if (e == PurchasesErrorCode.networkError) {
        return const Left(NetworkFailure());
      } else if (e == PurchasesErrorCode.storeProblemError) {
        return const Left(ServiceUnavailableFailure());
      }
      
      return Left(PurchaseFailure(e.toString()));
    } catch (e) {
      debugPrint('Purchase failed: $e');
      return Left(PurchaseFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, SubscriptionStatus>> restorePurchases() async {
    try {
      final customerInfo = await _revenueCatService.restorePurchases();
      if (customerInfo == null) {
        return const Left(RestoreFailure('Restore failed - no customer info returned'));
      }

      final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
      return Right(subscriptionStatus);
    } on PurchasesErrorCode catch (e) {
      debugPrint('Restore failed with error code: $e');
      
      if (e == PurchasesErrorCode.networkError) {
        return const Left(NetworkFailure());
      } else if (e == PurchasesErrorCode.storeProblemError) {
        return const Left(ServiceUnavailableFailure());
      }
      
      return Left(RestoreFailure(e.toString()));
    } catch (e) {
      debugPrint('Restore failed: $e');
      return Left(RestoreFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, SubscriptionStatus>> getSubscriptionStatus() async {
    try {
      final customerInfo = await _revenueCatService.getCustomerInfo();
      final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
      return Right(subscriptionStatus);
    } catch (e) {
      debugPrint('Failed to get subscription status: $e');
      return Left(StatusFetchFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, bool>> hasEntitlement(String entitlementIdentifier) async {
    try {
      final hasEntitlement = _revenueCatService.hasActiveSubscription(entitlementIdentifier);
      return Right(hasEntitlement);
    } catch (e) {
      debugPrint('Failed to check entitlement: $e');
      return Left(StatusFetchFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, SubscriptionStatus>> login(String appUserId) async {
    try {
      final customerInfo = await _revenueCatService.login(appUserId);
      final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
      return Right(subscriptionStatus);
    } catch (e) {
      debugPrint('Login failed: $e');
      return Left(LoginFailure(e.toString()));
    }
  }

  @override
  Future<Either<SubscriptionFailure, SubscriptionStatus>> logout() async {
    // try {
    //   final customerInfo = await _revenueCatService.logout();
    //   final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
    //   return Right(subscriptionStatus);
    // } catch (e) {
    //   debugPrint('Logout failed: $e');
      return const Left(LogoutFailure(""));
    // }
  }

  @override
  void setSubscriptionStatusUpdateListener(Function(SubscriptionStatus) onUpdate) {
    _revenueCatService.setCustomerInfoUpdateListener((customerInfo) {
      final subscriptionStatus = SubscriptionStatusModel.fromCustomerInfo(customerInfo).toDomain();
      onUpdate(subscriptionStatus);
    });
  }

  @override
  void removeSubscriptionStatusUpdateListener(Function(SubscriptionStatus) listener) {
    // Note: RevenueCat doesn't provide a direct way to remove specific listeners
    // This would need to be implemented with a wrapper if multiple listeners are needed
    debugPrint('Remove listener not implemented - RevenueCat limitation');
  }

  /// Determine if a product should be marked as popular
  /// This could be based on business logic, analytics, or configuration
  bool _isPopularProduct(String productIdentifier) {
    // Example: Mark yearly subscriptions as popular
    return productIdentifier.toLowerCase().contains('yearly') ||
           productIdentifier.toLowerCase().contains('annual');
  }
}
