import 'package:purchases_flutter/purchases_flutter.dart' as rc;
import '../../domain/entities/subscription_status.dart' as domain;

/// Data model for mapping RevenueCat CustomerInfo to domain entity
class SubscriptionStatusModel {
  const SubscriptionStatusModel({
    required this.isActive,
    required this.entitlements,
    required this.activeSubscriptions,
    required this.allPurchasedProductIdentifiers,
    required this.nonSubscriptionTransactions,
    this.originalAppUserId,
    this.firstSeen,
    this.originalPurchaseDate,
    this.requestDate,
    this.managementURL,
  });

  final bool isActive;
  final Map<String, domain.EntitlementInfo> entitlements;
  final Map<String, domain.SubscriptionInfo> activeSubscriptions;
  final Set<String> allPurchasedProductIdentifiers;
  final List<domain.Transaction> nonSubscriptionTransactions;
  final String? originalAppUserId;
  final DateTime? firstSeen;
  final DateTime? originalPurchaseDate;
  final DateTime? requestDate;
  final String? managementURL;

  /// Create SubscriptionStatusModel from RevenueCat CustomerInfo
  factory SubscriptionStatusModel.fromCustomerInfo(rc.CustomerInfo customerInfo) {
    return SubscriptionStatusModel(
      isActive: customerInfo.entitlements.active.isNotEmpty,
      entitlements: const {}, // Simplified for now
      activeSubscriptions: const {}, // Simplified for now
      allPurchasedProductIdentifiers: customerInfo.allPurchasedProductIdentifiers.toSet(),
      nonSubscriptionTransactions: const [], // Simplified for now
      originalAppUserId: customerInfo.originalAppUserId,
      firstSeen: null, // Parse date if needed
      originalPurchaseDate: null, // Parse date if needed
      requestDate: null, // Parse date if needed
      managementURL: customerInfo.managementURL,
    );
  }

  /// Convert to domain entity
  domain.SubscriptionStatus toDomain() {
    return domain.SubscriptionStatus(
      isActive: isActive,
      entitlements: entitlements,
      activeSubscriptions: activeSubscriptions,
      allPurchasedProductIdentifiers: allPurchasedProductIdentifiers,
      nonSubscriptionTransactions: nonSubscriptionTransactions,
      originalAppUserId: originalAppUserId,
      firstSeen: firstSeen,
      originalPurchaseDate: originalPurchaseDate,
      requestDate: requestDate,
      managementURL: managementURL,
    );
  }
}

// Simplified models - complex mapping removed for now
