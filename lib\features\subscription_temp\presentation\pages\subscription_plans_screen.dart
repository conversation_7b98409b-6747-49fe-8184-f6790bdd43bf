import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../common/extentions/colors_extension.dart';
import '../../../../common/widgets/app_image.dart';
import '../../../../common/widgets/large_button.dart';
import '../../../../generated/assets.dart';
import '../bloc/subscription_bloc.dart';
import '../widgets/subscription_product_card.dart';

/// Screen displaying available subscription plans
class SubscriptionPlansScreen extends StatefulWidget {
  const SubscriptionPlansScreen({super.key});

  @override
  State<SubscriptionPlansScreen> createState() => _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends State<SubscriptionPlansScreen> {
  @override
  void initState() {
    super.initState();
    // Load products when screen opens
    context.read<SubscriptionBloc>().add(const LoadSubscriptionProducts());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.background,
      appBar: AppBar(
        title: const Text('Choose Your Plan'),
        backgroundColor: context.surface,
        foregroundColor: context.onSurface,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: () {
              context.read<SubscriptionBloc>().add(const RestoreSubscriptions());
            },
            child: Text(
              'Restore',
              style: TextStyle(
                color: context.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: BlocConsumer<SubscriptionBloc, SubscriptionState>(
        listener: (context, state) {
          if (state.isPurchaseSuccess || state.isRestoreSuccess) {
            // Navigate back or to main screen
            Navigator.of(context).pop(true);
          } else if (state.hasError) {
            _showErrorDialog(context, state.errorMessage ?? 'Unknown error');
          }
        },
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state.hasError) {
            return _buildErrorState(context, state);
          }

          if (state.hasProducts) {
            return _buildProductsList(context, state);
          }

          return const Center(
            child: Text('Welcome! Loading subscription plans...'),
          );
        },
      ),
    );
  }

  /// Build error state UI
  Widget _buildErrorState(BuildContext context, SubscriptionState state) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: context.onBackground.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: context.textTheme.headlineSmall?.copyWith(
                color: context.onBackground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.errorMessage ?? 'Unknown error',
              textAlign: TextAlign.center,
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.onBackground.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 24),
            LargeButton(
              onPressed: () {
                context.read<SubscriptionBloc>().add(const LoadSubscriptionProducts());
              },
              text: 'Try Again',
              backgroundColor: context.primaryColor,
              textStyle: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build products list UI
  Widget _buildProductsList(BuildContext context, SubscriptionState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeader(context),
          const SizedBox(height: 32),

          // Products list
          ...state.products.map((product) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: SubscriptionProductCard(
                  product: product,
                  isLoading: state.isPurchasing,
                  onTap: () {
                    context.read<SubscriptionBloc>().add(
                          PurchaseSubscription(product.identifier),
                        );
                  },
                ),
              )),

          const SizedBox(height: 24),

          // Features list
          _buildFeaturesList(context),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  /// Build header section
  Widget _buildHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App logo
        Center(
          child: AppImage.asset(
            Assets.imagesOrangeAi,
            size: 48,
          ),
        ),
        const SizedBox(height: 24),
        
        // Title
        Text(
          'Unlock Premium Features',
          style: context.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: context.onBackground,
          ),
        ),
        const SizedBox(height: 8),
        
        // Subtitle
        Text(
          'Choose the plan that works best for you',
          style: context.textTheme.bodyLarge?.copyWith(
            color: context.onBackground.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  /// Build features list
  Widget _buildFeaturesList(BuildContext context) {
    const features = [
      'Unlimited food scanning',
      'Advanced nutrition tracking',
      'Personalized meal recommendations',
      'Export nutrition data',
      'Priority customer support',
      'Ad-free experience',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What\'s included:',
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: context.onBackground,
          ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: context.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: context.textTheme.bodyMedium?.copyWith(
                        color: context.onBackground,
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
