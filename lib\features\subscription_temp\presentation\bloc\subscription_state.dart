part of 'subscription_bloc.dart';

/// Enum representing the status of subscription operations
enum SubscriptionBlocStatus {
  /// Initial state before any operations
  initial,

  /// Loading state during operations
  loading,

  /// Products have been successfully loaded
  productsLoaded,

  /// Purchase is in progress
  purchasing,

  /// Purchase completed successfully
  purchaseSuccess,

  /// Purchases restored successfully
  restoreSuccess,

  /// Subscription status loaded successfully
  statusLoaded,

  /// Entitlement check completed
  entitlementChecked,

  /// An error occurred
  error,
}

/// State class for subscription management using status-based approach
class SubscriptionState extends Equatable {
  const SubscriptionState({
    this.status = SubscriptionBlocStatus.initial,
    this.products = const [],
    this.subscriptionStatus,
    this.hasEntitlement = false,
    this.errorMessage,
    this.isInitialized = false,
  });

  /// Current status of the subscription operations
  final SubscriptionBlocStatus status;

  /// List of available subscription products
  final List<SubscriptionProduct> products;

  /// Current subscription status
  final SubscriptionStatus? subscriptionStatus;

  /// Whether user has the checked entitlement
  final bool hasEntitlement;

  /// Error message when status is error
  final String? errorMessage;

  /// Whether the subscription service is initialized
  final bool isInitialized;

  /// Create a copy of this state with updated values
  SubscriptionState copyWith({
    SubscriptionBlocStatus? status,
    List<SubscriptionProduct>? products,
    SubscriptionStatus? subscriptionStatus,
    bool? hasEntitlement,
    String? errorMessage,
    bool? isInitialized,
    bool clearErrorMessage = false,
    bool clearSubscriptionStatus = false,
  }) {
    return SubscriptionState(
      status: status ?? this.status,
      products: products ?? this.products,
      subscriptionStatus: clearSubscriptionStatus 
          ? null 
          : (subscriptionStatus ?? this.subscriptionStatus),
      hasEntitlement: hasEntitlement ?? this.hasEntitlement,
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  /// Convenience getters for checking state
  bool get isLoading => status == SubscriptionBlocStatus.loading;
  bool get hasError => status == SubscriptionBlocStatus.error;
  bool get hasProducts => status == SubscriptionBlocStatus.productsLoaded && products.isNotEmpty;
  bool get isPurchasing => status == SubscriptionBlocStatus.purchasing;
  bool get isPurchaseSuccess => status == SubscriptionBlocStatus.purchaseSuccess;
  bool get isRestoreSuccess => status == SubscriptionBlocStatus.restoreSuccess;
  bool get isStatusLoaded => status == SubscriptionBlocStatus.statusLoaded;
  bool get isEntitlementChecked => status == SubscriptionBlocStatus.entitlementChecked;

  /// Check if user has any active subscription
  bool get hasActiveSubscription => subscriptionStatus?.isActive ?? false;

  /// Get popular products (marked as popular in the product list)
  List<SubscriptionProduct> get popularProducts => 
      products.where((product) => product.isPopular).toList();

  /// Get monthly products
  List<SubscriptionProduct> get monthlyProducts => products
      .where((product) => 
          product.subscriptionPeriod.unit == PeriodUnit.month && 
          product.subscriptionPeriod.value == 1)
      .toList();

  /// Get yearly products
  List<SubscriptionProduct> get yearlyProducts => products
      .where((product) => 
          product.subscriptionPeriod.unit == PeriodUnit.year && 
          product.subscriptionPeriod.value == 1)
      .toList();

  @override
  List<Object?> get props => [
        status,
        products,
        subscriptionStatus,
        hasEntitlement,
        errorMessage,
        isInitialized,
      ];
}
