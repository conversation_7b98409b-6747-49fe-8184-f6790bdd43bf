import 'package:purchases_flutter/purchases_flutter.dart' as rc;
import '../../domain/entities/subscription_product.dart';

/// Data model for mapping RevenueCat StoreProduct to domain entity
class SubscriptionProductModel extends SubscriptionProduct {
  const SubscriptionProductModel({
    required super.identifier,
    required super.title,
    required super.description,
    required super.price,
    required super.priceString,
    required super.currencyCode,
    required super.introPrice,
    required super.subscriptionPeriod,
    super.isPopular = false,
  });

  /// Create SubscriptionProductModel from RevenueCat StoreProduct
  factory SubscriptionProductModel.fromStoreProduct(
    rc.StoreProduct storeProduct, {
    bool isPopular = false,
  }) {
    return SubscriptionProductModel(
      identifier: storeProduct.identifier,
      title: storeProduct.title,
      description: storeProduct.description,
      price: storeProduct.price,
      priceString: storeProduct.priceString,
      currencyCode: storeProduct.currencyCode,
      introPrice: null, // TODO: Handle intro price from RevenueCat
      subscriptionPeriod: SubscriptionPeriodModel.fromProductId(storeProduct.identifier),
      isPopular: isPopular,
    );
  }

  /// Create SubscriptionProductModel from RevenueCat Package
  factory SubscriptionProductModel.fromPackage(
    rc.Package package, {
    bool isPopular = false,
  }) {
    return SubscriptionProductModel.fromStoreProduct(
      package.storeProduct,
      isPopular: isPopular,
    );
  }

  /// Convert to domain entity
  SubscriptionProduct toDomain() {
    return SubscriptionProduct(
      identifier: identifier,
      title: title,
      description: description,
      price: price,
      priceString: priceString,
      currencyCode: currencyCode,
      introPrice: introPrice,
      subscriptionPeriod: subscriptionPeriod,
      isPopular: isPopular,
    );
  }
}

/// Data model for mapping RevenueCat StoreProductDiscount to domain entity
class IntroPriceModel extends IntroPrice {
  const IntroPriceModel({
    required super.price,
    required super.priceString,
    required super.period,
    required super.cycles,
  });

  /// Create IntroPriceModel from RevenueCat StoreProductDiscount
  factory IntroPriceModel.fromStoreProductDiscount(
    rc.StoreProductDiscount discount,
  ) {
    return IntroPriceModel(
      price: discount.price,
      priceString: discount.priceString,
      period: SubscriptionPeriodModel.fromDefault(),
      cycles: discount.cycles,
    );
  }

  /// Convert to domain entity
  IntroPrice toDomain() {
    return IntroPrice(
      price: price,
      priceString: priceString,
      period: period,
      cycles: cycles,
    );
  }
}

/// Data model for mapping RevenueCat SubscriptionPeriod to domain entity
class SubscriptionPeriodModel extends SubscriptionPeriod {
  const SubscriptionPeriodModel({
    required super.unit,
    required super.value,
  });

  /// Create default subscription period (monthly)
  factory SubscriptionPeriodModel.fromDefault() {
    return const SubscriptionPeriodModel(
      unit: PeriodUnit.month,
      value: 1,
    );
  }

  /// Create subscription period from product identifier
  factory SubscriptionPeriodModel.fromProductId(String productId) {
    final lowerId = productId.toLowerCase();
    if (lowerId.contains('yearly') || lowerId.contains('annual')) {
      return const SubscriptionPeriodModel(
        unit: PeriodUnit.year,
        value: 1,
      );
    } else if (lowerId.contains('weekly')) {
      return const SubscriptionPeriodModel(
        unit: PeriodUnit.week,
        value: 1,
      );
    } else {
      // Default to monthly
      return const SubscriptionPeriodModel(
        unit: PeriodUnit.month,
        value: 1,
      );
    }
  }

  /// Convert to domain entity
  SubscriptionPeriod toDomain() {
    return SubscriptionPeriod(
      unit: unit,
      value: value,
    );
  }
}
