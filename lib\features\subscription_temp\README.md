# Subscription Temp Feature

This is a temporary implementation of the subscription feature using RevenueCat instead of the previous in_app_purchase implementation.

## Architecture

This feature follows the Clean Architecture pattern with the following structure:

- **data/**: Data layer containing repositories, data sources, and models
- **domain/**: Domain layer containing entities, repositories interfaces, and use cases
- **presentation/**: Presentation layer containing BLoC/Cubit, pages, and widgets
- **di/**: Dependency injection setup

## RevenueCat Integration

This implementation uses the `purchases_flutter` package for RevenueCat integration, providing:

- Product fetching
- Purchase handling
- Subscription status checking
- Receipt validation
- Restore purchases functionality

## Usage

The subscription feature is integrated into the app flow after onboarding and provides premium features access.
